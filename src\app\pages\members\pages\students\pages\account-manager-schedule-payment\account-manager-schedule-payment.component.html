<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">
      {{ isRePayment ? 'Retry Payment' : 'Enrolment and payment' }}
    </div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button"
        (click)="onCloseModal()">Close</button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : enrollLesson"></ng-container>
  </div>
</div>

<ng-template #enrollLesson>
  <ng-container [ngTemplateOutlet]="isPaymentDone ? confirmationForPaymentTemp : scheduleClassDetail"></ng-container>
</ng-template>

<ng-template #confirmationForPaymentTemp>
  <div class="lesson-cancel-wrapper">
    <div>
      <img [src]="constants.staticImages.icons.checkCircle" height="80" width="80" alt="" />
      <div class="success-title">
        {{ selectedInstrumentName }} Lesson ({{ schedulerService.getLessonType(scheduleInfo.lessonType) }}, {{
        scheduleInfo.skillType }})
      </div>
      <div class="success-message">
        <div>
          {{ selectedInstrumentName }} Lesson ({{ schedulerService.getLessonType(scheduleInfo.lessonType) }}, {{
          scheduleInfo.skillType }})
        </div>
        <div>
          with <span>{{ selectedInstructorName }}</span> at
        </div>
        <div>
          <span>{{ selectedLocation }}</span> on
          <span>{{ scheduleInfo.scheduleDate | localDate | date : constants.dateFormats.MMM_d }},
            {{ scheduleInfo.scheduleStartTime || scheduleInfo.start | localDate | date : 'shortTime' }} - {{ scheduleInfo.scheduleEndTime || scheduleInfo.end | localDate | date :
            'shortTime' }}</span>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #scheduleClassDetail>
  <div class="group-class-detail-wrapper">
    <div class="schedule-information-title">
      <div>Schedule Information</div>
    </div>
    <div class="schedule-basic-details">
      <div class="schedule-info-header">
        <div class="group-name-age">
          {{ schedulerService.getLessonType(scheduleInfo.lessonType) }} {{ selectedInstrumentName }} Lesson ({{
          scheduleInfo.skillType }})
        </div>
      </div>

      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.location" alt="" />
        <div class="schedule-info-content">
          {{ selectedLocation }}
        </div>
      </div>
      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.profileCircle" alt="" />
        <div class="schedule-info-content"><span class="gray-text">With</span> {{ selectedInstructorName }}</div>
      </div>
      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
        <div class="schedule-info-content">
          {{ scheduleInfo.scheduleDate | localDate | date : 'mediumDate' }}
        </div>
      </div>

      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.timeCircleClock" alt="" />
        <div class="schedule-info-content">
          {{ scheduleInfo.scheduleStartTime || scheduleInfo.start | localDate | date : 'shortTime' }} - {{ scheduleInfo.scheduleEndTime || scheduleInfo.end | localDate | date :
          'shortTime' }} (30 Min)
        </div>
      </div>
      <div class="payment-info">
        <div class="dotted-divider"></div>
        <div class="schedule-info-content">
          <div class="payment-details-wrapper">
            <div class="title">Payment Details</div>
            <div class="payment-details-content">
              <div class="space-between mb-4">
                <div class="sub-title">
                  {{ schedulerService.getLessonType(scheduleInfo.lessonType) }} {{
                  getInstrumentNameFromValue(scheduleInfo.instrumentId) }} Lesson
                </div>
                <div class="plan-content mb-0">${{ scheduleInfo.price ??
                  constants.defaultAmountPayableForIntroductoryClasses }}</div>
              </div>

              <div class="space-between discount-row mb-4" *ngIf="!showDiscountField">
                @if (this.constants.roleIds.CLIENT === currentUser?.userRoleId) {
                <div class="sub-title">Discount</div>
                }@else {
                <div class="sub-title mb-3 discount-toggle" (click)="showDiscountField = true">
                  <span class="discount-link">Apply discount</span>
                </div>
                }
                <div class="plan-content">$0.00</div>
              </div>

              <div class="space-between discount-row mb-4" *ngIf="showDiscountField">
                <div class="sub-title mb-3 discount-input-container">
                  <mat-form-field>
                    <mat-label>Discount Amount</mat-label>

                    <input matInput pattern="[0-9]*\.?[0-9]*" [(ngModel)]="discountAmount"
                      (ngModelChange)="onDiscountChange()" (keydown)="preventInvalidInput($event)" placeholder="0.00">
                    <span matPrefix>$&nbsp;</span>
                    <button mat-icon-button matSuffix (click)="clearDiscount()" [attr.aria-label]="'Clear discount'"
                      type="button">
                      <mat-icon>close</mat-icon>
                    </button>
                  </mat-form-field>
                  <div class="error-message" *ngIf="discountError">{{ discountError }}</div>
                </div>
                <div class="plan-content">${{ discountAmount | number:'1.2-2' }}</div>
              </div>

              <!-- to be use for recurring Registration Fee -->
              <!-- <div class="space-between mb-4">
                <div class="sub-title">Registration Fee</div>
                <div class="plan-content mb-0">${{ registrationFee | number:'1.2-2' }}</div>
              </div> -->

              <!-- Security Deposit -->
              <!-- to be use for recurring <div class="space-between mb-4">
                <div class="sub-title">Security Deposit</div>
                <div class="plan-content mb-0">${{ securityDeposit | number:'1.2-2' }}</div>
              </div> -->

              <div class="dotted-divider"></div>
              <div class="space-between">
                <div class="sub-title text-black">Total Payable Amount</div>
                <div class="plan-content mb-0">${{ totalPayableAmount | number:'1.2-2' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="schedule-information-title">
      <div>Add Payment Details</div>
    </div>
    <div>
      <app-payment-methods screen="account-manager-schedule-payment" [showDefaultPaymentBtn]="false" [accManagerDetails]="accManagerDetails" [isPaymentFailed]="isRePayment" (closeSideNav)="onCloseModal()"
      ></app-payment-methods>
    </div>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>