import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule, DatePipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { CBResponse, IdNameModel } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { CommonUtils } from 'src/app/shared/utils';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { NgxPaginationModule } from 'ngx-pagination';
import { Debounce } from 'src/app/shared/decorators';
import { SummerCampScheduleService } from './services';
import { SummerCampDetails, SummerCampFilters } from './models';
import { AddSummerCampComponent } from './pages/add-summer-camp/add-summer-camp.component';
import { UpdateSummerCampComponent } from './pages/update-summer-camp/update-summer-camp.component';
import { All } from '../plan/models';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { ViewSummerCampComponent } from './pages/view-summer-camp/view-summer-camp.component';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { Router, ActivatedRoute } from '@angular/router';
import { DateFilterTypeEnum } from 'src/app/pages/schedule-classes/pages/group-class/models';
import { MultiSelectComponent } from 'src/app/shared/components/multi-select/multi-select.component';
import { DateUtils } from 'src/app/shared/utils/date.utils';

const DEPENDENCIES = {
  MODULES: [
    SharedModule,
    MatSidenavModule,
    MatSelectModule,
    MatButtonModule,
    CommonModule,
    FormsModule,
    MatInputModule,
    MatIconModule,
    NgxPaginationModule
  ],
  COMPONENTS: [AddSummerCampComponent, UpdateSummerCampComponent, ViewSummerCampComponent, MultiSelectComponent]
};

@Component({
  selector: 'app-summer-camp-creation',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './summer-camp-creation.component.html',
  styleUrl: './summer-camp-creation.component.scss'
})
export class SummerCampCreationComponent extends BaseComponent implements OnInit {
  isAddSummerCampSideNavOpen = false;
  isViewSummerCampSideNavOpen = false;
  isEditFromView = false;

  selectedSummerCamp!: SummerCampDetails | null;
  summerCampDetails: Array<SummerCampDetails> = [];

  pageSize = this.paginationConfig.itemsPerPage;
  currentPage = this.paginationConfig.pageNumber;
  totalCount!: number;
  locations!: Array<SchoolLocations>;

  pageTabOptions = { ONGOING: 'Ongoing Camp', UPCOMING: 'Upcoming Camp', PAST: 'Past Camp' };
  selectedTabOption = this.pageTabOptions.ONGOING;

  all = All;
  filters: SummerCampFilters = {
    searchFilter: null,
    ageGroup: 0,
    locationId: {
      id: 1,
      defaultPlaceholder: 'All Locations',
      placeholder: 'All Locations',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    currentDateFilter: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss)
  };

  constructor(
    private readonly summerCampScheduleService: SummerCampScheduleService,
    private readonly commonService: CommonService,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService,
    protected readonly schedulerService: SchedulerService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
    private readonly datePipe: DatePipe
  ) {
    super();
  }

  ngOnInit(): void {
    this.setActiveTabFromQueryParams();
    this.getLocations();
  }

  toggleAddEditSideNav(isOpen: boolean, isView: boolean, summerCampId: SummerCampDetails | null, isEditFromView = false): void {
    this.isAddSummerCampSideNavOpen = isOpen;
    this.isViewSummerCampSideNavOpen = isView;
    this.selectedSummerCamp = summerCampId;
    this.isEditFromView = isEditFromView;
  }

  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (Object.keys(params).length) {
        this.selectedTabOption = params.activeTab;
        this.getSummerCampDetail(this.currentPage, this.pageSize);
        return;
      }
      this.setActiveTabOption(this.pageTabOptions.ONGOING);
    });
  }

  setActiveTabOption(tabName: string): void {
    this.selectedTabOption = tabName;
    this.router.navigate([this.path.settings.root, this.path.settings.summerCamp], {
      queryParams: {
        activeTab: tabName
      }
    });
  }

  getDateTypeFilter(): number {
    switch (this.selectedTabOption) {
      case this.pageTabOptions.PAST:
        return DateFilterTypeEnum.PAST_CLASSES;
      case this.pageTabOptions.UPCOMING:
        return DateFilterTypeEnum.UPCOMING_CLASSES;
      default:
        return DateFilterTypeEnum.ONGOING_CLASS;
    }
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.getSummerCampDetail(this.currentPage, this.pageSize);
  }

  getFilterParams(currentPage: number, pageSize: number) {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      Filter: this.filters.searchFilter,
      AgeGroupFilter: this.filters.ageGroup,
      LocationIdFilter: [...this.filters.locationId.value],
      scheduleStartDate: DateUtils.getUtcRangeForLocalDate(this.filters.currentDateFilter ?? '').startUtc,
      scheduleEndDate: DateUtils.getUtcRangeForLocalDate(this.filters.currentDateFilter ?? '').endUtc,
      DateFilterType: this.getDateTypeFilter(),
      Page: currentPage,
      PageSize: pageSize
    });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.filters.locationId.options = res.result.items.map(location => ({
            id: location.schoolLocations.id,
            name: location.schoolLocations.locationName
          }));
          this.filters.locationId.totalCount = this.locations.length;
          this.cdr.detectChanges();
        }
      });
  }

  getSummerCampDetail(currentPage: number, pageSize: number): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();
    this.summerCampScheduleService
      .add(this.getFilterParams(currentPage, pageSize), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SummerCampDetails>) => {
          this.summerCampDetails = res.result.items.map(item => ({
            ...item,
            summerCampScheduleSummary: {
              ...item.summerCampScheduleSummary,
              scheduleStartDate: DateUtils.toLocal(item.summerCampScheduleSummary.scheduleStartDate, 'yyyy-MM-DDTHH:mm:ss'),
              scheduleEndDate: DateUtils.toLocal(item.summerCampScheduleSummary.scheduleEndDate, 'yyyy-MM-DDTHH:mm:ss'),
              enrollLastDate: DateUtils.toLocal(item.summerCampScheduleSummary.enrollLastDate, 'yyyy-MM-DDTHH:mm:ss'),
              scheduleStartTime: DateUtils.toLocal(item.summerCampScheduleSummary.scheduleStartTime, 'yyyy-MM-DDTHH:mm:ss'),
              scheduleEndTime: DateUtils.toLocal(item.summerCampScheduleSummary.scheduleEndTime, 'yyyy-MM-DDTHH:mm:ss')
            }
          }));
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  deleteSummerCampConfirmation(id: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Summer Camp`,
        message: `Are you sure you want to delete this summer camp?`
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result.isConfirmed) {
        this.deleteSummerCamp(id);
      }
    });
  }

  deleteSummerCamp(id: number): void {
    this.summerCampScheduleService
      .delete(id, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Summer Camp'));
          this.getSummerCampDetail((this.currentPage = 1), this.pageSize);
        }
      });
  }

  onCloseSideNav(): void {
    this.isAddSummerCampSideNavOpen = false;
  }

  keepOriginalOrder = () => 0;

  @Debounce(300)
  onSearchTermChanged(): void {
    this.currentPage = 1;
    this.getSummerCampDetail(this.currentPage, this.pageSize);
  }
}
