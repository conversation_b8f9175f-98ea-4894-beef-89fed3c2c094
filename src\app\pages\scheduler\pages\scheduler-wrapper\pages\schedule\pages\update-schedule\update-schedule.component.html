<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">Edit Schedule</div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="onCloseModal()">
        Close
      </button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="onUpdateSchedule()"
        [appLoader]="showBtnLoader">
        Save
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : updateScheduleTemplate"></ng-container>
  </div>
</div>

<ng-template #updateScheduleTemplate>
  <div class="schedule-info-wrapper">
    <div class="schedule-header">
      <div class="name">
        @switch (selectedEvent.classType) {
          @case (classTypes.GROUP_CLASS) {
            {{ selectedEvent.groupClassName | titlecase }} ({{ getTimeDiff(selectedEvent.start, selectedEvent.end) }})
          }
          @case (classTypes.ENSEMBLE_CLASS) {
            {{ selectedEvent.ensembleClassName | titlecase }} ({{ getTimeDiff(selectedEvent.start, selectedEvent.end) }})
          }
          @case (classTypes.SUMMER_CAMP) {
            {{ selectedEvent.campName | titlecase }} ({{
              schedulerService.getNumberOfDays(selectedEvent.campStartDate, selectedEvent.campEndDate)
            }}d)
          }
          @case (classTypes.MAKE_UP) {
            {{ selectedEvent.instrumentName }} Make-Up Lesson ({{
              getTimeDiff(selectedEvent.start, selectedEvent.end)
            }})
          }
          @case (classTypes.INTRODUCTORY) {
            Introductory {{ selectedEvent.instrumentName }} Lesson ({{
              getTimeDiff(selectedEvent.start, selectedEvent.end)
            }})
          }
          @default {
            {{ selectedEvent.instrumentName }} Lesson ({{ getTimeDiff(selectedEvent.start, selectedEvent.end) }})
          }
        }
      </div>
      <div class="class-info-wrapper">
        <div>{{ schedulerService.getClassType(selectedEvent.classType) }}</div>
        <div class="dot"></div>
        <div>
          {{ schedulerService.getLessonType(selectedEvent.lessonType) }}
        </div>
      </div>
    </div>
    <div class="schedule-content-wrapper">
      <div class="schedule-content">
        <img
          class="instructor-img"
          [src]="
            selectedEvent.instructorProfilePhoto
              ? selectedEvent.instructorProfilePhoto
              : constants.staticImages.images.profileImgPlaceholder
          "
          alt="" />
        <div class="info-content" *ngIf="selectedEvent?.classType !== classTypes.ENSEMBLE_CLASS">
          <span class="info-label">Instructor</span>
          <span class="primary-text ms-1">{{ selectedEvent.instructorName }}</span>
        </div>

        <div class="info-content" *ngIf="selectedEvent?.classType == classTypes.ENSEMBLE_CLASS">
          <span class="info-label">Instructor</span>
          <span class="primary-text display-content ms-1" >
            @for (assignedInstructors of selectedEvent.assignedInstructors; track $index) {
            <ng-container *ngIf="$index < 1">
              {{ assignedInstructors?.name }}
            </ng-container>
            } @if (selectedEvent.assignedInstructors!.length > 1) {
              <div class="dot" ></div>
            <div class="remaining-instrument-available-count" [matTooltip]="getInstructorNames(selectedEvent!.assignedInstructors)">
              {{ (selectedEvent.assignedInstructors)!.length - 1 }}+
            </div>
            }
          </span>
        </div>

      </div>
      @if (selectedEvent.studentDetails.length && selectedEvent) {
        <div class="dot"></div>
        <div class="schedule-content">
          <img [src]="constants.staticImages.icons.profileCircle" alt="" class="img-icon" />
          <div class="info-content">
            <span class="info-label">Client</span>
            <span class="primary-text ms-1"
              >{{ selectedEvent.studentDetails[0].studentName | titlecase }}
              @if (selectedEvent.studentDetails.length > 1) {
                (+ {{ selectedEvent.studentDetails.length - 1 }})
              }
            </span>
          </div>
        </div>
      } @else {
        <div class="dot"></div>
        <div class="schedule-content">
          <img [src]="constants.staticImages.icons.profileIcon" class="instructor-img" alt="" />
          <div class="info-content">
            Age Group
            <span class="primary-color">{{
              schedulerService.getAgeLabelFromValue(groupClassInfo?.groupClassScheduleSummary?.ageGroup)
            }}</span>
          </div>
        </div>
      }
    </div>
    <div class="schedule-content-wrapper">
      @if (selectedEvent.classType !== classTypes.GROUP_CLASS) {
        <div class="schedule-content">
          <img [src]="constants.staticImages.icons.timeCircleClock" alt="" class="img-icon" />
          <div class="info-content">
            {{ selectedEvent.start | localDate | date: "shortTime" }}
            -
            {{ selectedEvent.end | localDate | date: "shortTime" }} -
            {{ selectedEvent.start | localDate | date: constants.fullDate }}
          </div>
        </div>
      } @else {
        <div class="schedule-content">
          <img [src]="constants.staticImages.icons.skill" alt="" class="img-icon" />
          <div class="info-content">
            {{ groupClassInfo?.groupClassScheduleSummary?.skillType }}
          </div>
        </div>
      }
      <div class="dot"></div>

      <div class="schedule-content">
        <img [src]="constants.staticImages.icons.location" alt="" class="img-icon" />
        <div class="info-content">
          {{ selectedEvent.locationName }}
        </div>
      </div>
      @if (selectedEvent.roomName) {
        <div class="dot"></div>
        <div class="schedule-content">
          <img [src]="constants.staticImages.icons.roomIcon" alt="" class="img-icon" />
          <div class="info-content">
            {{ selectedEvent.roomName }}
          </div>
        </div>
      }
      @if (
        selectedEvent.classType === classTypes.GROUP_CLASS &&
        groupClassInfo?.groupClassScheduleSummary?.isWaitlistAvailable
      ) {
        <div class="dot"></div>
        <div class="schedule-content">
          <img [src]="constants.staticImages.icons.checkSquare" alt="" class="img-icon" />
          <div class="info-content">Waitlist Available</div>
        </div>
      }
    </div>
    @if (selectedEvent.classType === classTypes.GROUP_CLASS) {
      <div class="schedule-content-wrapper">
        <div class="schedule-content">
          <img [src]="constants.staticImages.icons.calendarIcon" alt="" class="img-icon" />
          <div class="info-content">
            Last Date of Enrollment
            <span class="primary-color">{{
              groupClassInfo?.groupClassScheduleSummary?.enrollLastDate | localDate | date: "mediumDate"
            }}</span>
          </div>
        </div>
        <div class="dot"></div>
        <div class="schedule-content">
          <img [src]="constants.staticImages.icons.circleDollar" alt="" class="img-icon" />
          <div class="info-content">
            Price <span class="primary-color">${{ groupClassInfo?.groupClassScheduleSummary?.price }}</span>
          </div>
        </div>
      </div>
    }
  </div>
  <form [formGroup]="updateScheduleForm" class="mt-3">
    @if (selectedEvent.classType === classTypes.GROUP_CLASS || selectedEvent.classType === classTypes.RECURRING) {
      <div class="field-wrapper">
        <label class="required">Select Edit Option</label>
        <div>
          <div class="btn-typed-options-wrapper">
            @for (recurringScheduleOption of constants.editRecurringScheduleOptions; track $index) {
              <div
                [ngClass]="{
                  'btn-typed-option': true,
                  active: updateScheduleForm.controls.isAllInstances.value === recurringScheduleOption.value
                }"
                (click)="
                  setFormControlValue('isAllInstances', recurringScheduleOption.value);
                  onRecurringEditOptionChange(recurringScheduleOption.value,classTypes.GROUP_CLASS)
                ">
                {{ recurringScheduleOption.label }}
              </div>
            }
          </div>
          <mat-error
            *ngIf="
              !updateScheduleForm.controls.isAllInstances.value &&
              (updateScheduleForm.controls.isAllInstances.touched || updateScheduleForm.controls.isAllInstances.dirty)
            "
            class="mat-error-position">
            <app-error-messages [control]="updateScheduleForm.controls.classType"></app-error-messages>
          </mat-error>
        </div>
      </div>
    }

    <div class="field-wrapper field-with-mat-inputs">
      <label class="required mb-3">{{
        updateScheduleForm.getRawValue().isAllInstances && selectedEvent.classType === classTypes.GROUP_CLASS
          ? "Start Date - End Date"
          : "Start Date"
      }}</label>
      <div class="field-content">
        <mat-form-field class="mat-start-date">
          <input
            (dateChange)="clearScheduleEndDate(); resetInstructor()"
            matInput
            [matDatepicker]="picker"
            (click)="picker.open()"
            formControlName="scheduleStartDate"
            placeholder="Select Start Date"
            [min]="
              scheduleInfo?.recurringScheduleDate ??
              transformedDate! < groupClassInfo?.groupClassScheduleSummary?.scheduleStartDate!
                ? transformedDate
                : groupClassInfo?.groupClassScheduleSummary?.scheduleStartDate
            " />
          <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
          <mat-error>
            <app-error-messages [control]="updateScheduleForm.controls.scheduleStartDate"></app-error-messages>
          </mat-error>
        </mat-form-field>
        @if (updateScheduleForm.getRawValue().isAllInstances && selectedEvent.classType === classTypes.GROUP_CLASS) {
          <div class="dash mb-4">-</div>
          <mat-form-field class="mat-start-date w-100">
            <input
              (ngModelChange)="getSuggestedTime(true,selectedEvent.classType)"
              matInput
              [matDatepicker]="endPicker"
              (click)="endPicker.open()"
              formControlName="scheduleEndDate"
              placeholder="Select End Date"
              [min]="
                updateScheduleForm.controls.scheduleStartDate.value
                  ? updateScheduleForm.controls.scheduleStartDate.value
                  : maxDate
              " />
            <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
            <mat-datepicker #endPicker></mat-datepicker>
            <mat-error>
              <app-error-messages [control]="updateScheduleForm.controls.scheduleEndDate"></app-error-messages>
            </mat-error>
          </mat-form-field>
        }
      </div>
    </div>

    @if (
      updateScheduleForm.controls.classType.value !== classTypes.INTRODUCTORY &&
      updateScheduleForm.controls.isAllInstances.value
    ) {
      <div class="field-wrapper">
        <label class="required mb-3">Select days of the week</label>
        <div>
          <div class="single-btn-select-wrapper">
            @for (day of constants.daysOfTheWeek; track $index) {
              <div
                [ngClass]="{ active: isDaySelected(day.value) }"
                class="select-btn"
                (click)="setDaysOfWeek(day.value)">
                {{ day.label }}
              </div>
            }
          </div>
          <mat-error
            *ngIf="
              !updateScheduleForm.controls.daysOfSchedule.value.length &&
              updateScheduleForm.controls.daysOfSchedule.errors?.['required']
            "
            class="mat-error-position">
            <app-error-messages [control]="updateScheduleForm.controls.daysOfSchedule"></app-error-messages>
          </mat-error>
        </div>
      </div>
    }

    @if (selectedEvent.classType !== classTypes.SUMMER_CAMP) {
      <div class="field-wrapper">
        <label class="required">Select Time Slot</label>
        <div class="w-100">
          <mat-form-field class="w-100 mat-select-custom time">
            <mat-select [placeholder]="selectedTimeSlot ?? 'Select Time'">
              <mat-option
                *ngFor="let suggestedTimeSlot of suggestedTimeSlots"
                (click)="setStartAndEndTime(suggestedTimeSlot)"
                [value]="suggestedTimeSlot.startTime + ' - ' + suggestedTimeSlot.endTime">
                {{ suggestedTimeSlot.startTime | date: "shortTime" }} -
                {{ suggestedTimeSlot.endTime | date: "shortTime" }}
              </mat-option>
              @if (!suggestedTimeSlots?.length) {
                <mat-option>No Time Slot Available</mat-option>
              }
            </mat-select>
          </mat-form-field>
          <mat-error
            *ngIf="
              !updateScheduleForm.controls.scheduleStartTime.value &&
              (updateScheduleForm.controls.scheduleStartTime.touched ||
                updateScheduleForm.controls.scheduleStartTime.dirty)
            ">
            <app-error-messages [control]="updateScheduleForm.controls.scheduleStartTime"></app-error-messages>
          </mat-error>
        </div>
      </div>
    } @else {
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required mb-4">Select Duration</label>
        <div class="field-content">
          <div class="w-100">
            <ngx-timepicker-field
              formControlName="scheduleStartTime"
              [minutesGap]="constants.fiveMinutesGap"
              (timeChanged)="setTimeInit('scheduleStartTime'); resetInstructor()"></ngx-timepicker-field>
            <mat-error>
              <app-error-messages [control]="updateScheduleForm.controls.scheduleStartTime"></app-error-messages>
            </mat-error>
          </div>
          <div class="dash mb-4">-</div>
          <div class="w-100">
            <ngx-timepicker-field
              formControlName="scheduleEndTime"
              [minutesGap]="constants.fiveMinutesGap"
              (timeChanged)="setTimeInit('scheduleEndTime'); resetInstructor()"></ngx-timepicker-field>
            <mat-error>
              <app-error-messages [control]="updateScheduleForm.controls.scheduleEndTime"></app-error-messages>
            </mat-error>
          </div>
        </div>
      </div>

      <div class="field-wrapper field-with-mat-inputs">
        <label class="required mb-4">Select Instructor</label>
        <div class="w-100">
          <mat-form-field class="w-100 mat-select-custom">
            <mat-select
              formControlName="instructorId"
              [placeholder]="selectedEvent.instructorName ?? 'Select Instructor'">
              @if (instructors && instructors.length) {
                <mat-option *ngFor="let instructor of instructors" [value]="instructor.id">
                  <div class="instructor-name">{{ instructor.name }}</div>
                </mat-option>
              } @else {
                <mat-option>No Instructor Available</mat-option>
              }
            </mat-select>
            <mat-error>
              <app-error-messages [control]="updateScheduleForm.controls.instructorId"></app-error-messages>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    }

    @if (updateScheduleForm.controls.classType.value === classTypes.INTRODUCTORY) {
      <div class="field-wrapper field-with-mat-inputs">
        <label>Special Needs Client</label>
        <mat-checkbox formControlName="isSpecialNeedsLesson" class="special-need">
          A Client With Special Need
        </mat-checkbox>
      </div>
    }
    <div class="dotted-divider"></div>
    <div>
      <label>Notes</label>
      <div>
        <mat-form-field>
          <textarea
            matInput
            formControlName="notes"
            cdkTextareaAutosize
            cdkAutosizeMinRows="3"
            cdkAutosizeMaxRows="10"
            placeholder="Describe the changes in the schedule to client"></textarea>
          <mat-error>
            <app-error-messages [control]="updateScheduleForm.controls.notes"></app-error-messages>
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </form>

  @if (selectedEvent.classType === classTypes.GROUP_CLASS) {
    <div class="student-detail-wrapper">
      <div class="student-detail-header">
        Registered Clients
        <span class="primary-color">
          {{ groupClassInfo?.scheduleStudentDetails?.length }}/{{
            groupClassInfo?.groupClassScheduleSummary?.studentCapacity
          }}
        </span>
      </div>
      @for (studentDetail of groupClassInfo?.scheduleStudentDetails; track $index) {
        <div class="student-detail-content">
          <img class="me-2" [src]="constants.staticImages.icons.profileIcon" alt="" />
          <div class="info-content fw-bold">{{ studentDetail.studentName }}</div>
        </div>
        <div class="student-detail-content ms-4">
          <div class="text-gray me-1">Account Manager:</div>
          <span>{{ studentDetail.accountManagerName }}</span>
        </div>
        <div class="d-flex justify-content-between w-75">
          <div class="student-detail-content ms-4 text-gray">
            <img [src]="constants.staticImages.icons.email" class="account-manager-detail-icon me-1" alt="" />
            {{ studentDetail.accountManagerEmail }}
          </div>
          <div class="student-detail-content text-gray">
            <img [src]="constants.staticImages.icons.phone" class="account-manager-detail-icon me-1" alt="" />
            {{ studentDetail.accountManagerPhoneNo }}
          </div>
        </div>
        <div class="dotted-divider" *ngIf="$index < groupClassInfo?.scheduleStudentDetails?.length! - 1"></div>
      }
    </div>
  }
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
