<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isPlanAssignedSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-750"
    [disableClose]="true"
  >
    @if (isPlanAssignedSideNavOpen) {
    <app-plan-assigned-success
      [selectedStudentDetails]="selectedStudentDetails"
      [selectedStudentPlan]="selectedStudentPlan"
      [selectedEnsembleClass]="selectedEnsembleClass"
      [totalAmount]="selectedStudentPlan!.planPrice"
      (closeSideNav)="onCloseSideNav()"
    ></app-plan-assigned-success>
    }
  </mat-sidenav>
</mat-sidenav-container>

<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="back-btn-wrapper" (click)="onCloseSideNav()">
      <img [src]="constants.staticImages.icons.arrowLeft" class="pointer" alt="" />
      <div class="ps-2">
        @if (isShowPaymentTempOpen && !isRePayment) {
        <div class="title">Payment</div>
        } @else if (isRePayment) {
        <div class="title">Retry Payment</div>
        } @else {
        <div class="title">Ensemble Classes</div>
        }
        <div class="name">
          <img [src]="constants.staticImages.icons.profileIcon" class="pe-1" alt="" />
          {{ selectedStudentDetails?.firstName | titlecase }}
          {{ selectedStudentDetails?.lastName | titlecase }}
        </div>
      </div>
    </div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="onCloseSideNav()">Close</button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : ensembleClassTemp"></ng-container>
  </div>
</div>

<ng-template #ensembleClassTemp>
  <ng-container [ngTemplateOutlet]="ensembleClassDetails.length ? cardOrEnsembleTemp : noDataFound"></ng-container>
</ng-template>

<ng-template #cardOrEnsembleTemp>
  <ng-container [ngTemplateOutlet]="isShowPaymentTempOpen ? showPaymentTemp : ensembleClassInfoTemp"></ng-container>
</ng-template>

<ng-template #ensembleClassInfoTemp>
  @for (ensembleClassDetail of ensembleClassDetails; track $index) {
  <div class="ensemble-schedule">
    <div class="student-schedule">
      <div class="student-content mb-1">
        <div class="schedule-date">{{ ensembleClassDetail.scheduleStartDate | localDate | date : constants.fullDate }}</div>
        <div class="fw-bold">
          {{ ensembleClassDetail.scheduleStartTime | localDate | date : constants.dateFormats.hh_mm_a }} -
          {{ ensembleClassDetail.scheduleEndTime | localDate | date : constants.dateFormats.hh_mm_a }}
        </div>
      </div>
      <div class="student-content fw-bold mb-1">
        <div>{{ ensembleClassDetail?.ensembleClassName | titlecase }}</div>
      </div>
      <div class="student-content mb-1">
        <div class="student-content">
          <img [src]="constants.staticImages.icons.location" class="black-img" alt="" />
          <div class="location">{{ ensembleClassDetail.locationName }}</div>
        </div>
        <div class="dot"></div>
        <div class="student-content">
          <img [src]="constants.staticImages.icons.profileCircle" class="black-img" alt="" />
          @for (assignedInstructors of ensembleClassDetail.assignedInstructors; track $index) {
          <ng-container *ngIf="$index < 1">
            {{ assignedInstructors?.instructorName }}
            <div class="dot hide-sm-screen-devices" *ngIf="!$last"></div>
          </ng-container>
          } @if (ensembleClassDetail.assignedInstructors!.length>1) {
          <div class="remaining-instrument-available-count" [matTooltip]="getInstructorNames(ensembleClassDetail.assignedInstructors)">
            {{ ensembleClassDetail.assignedInstructors!.length - 1 }}+
          </div>
          }
        </div>
      </div>
      <div class="student-content">
        <div class="student-content">
          <img [src]="constants.staticImages.icons.instrumentIcon" class="black-img" alt="" />
          @for (assignedInstruments of ensembleClassDetail.assignedInstruments; track $index) {
          <ng-container *ngIf="$index < 1">
            {{ assignedInstruments?.instrumentName }}
            <div class="dot hide-sm-screen-devices" *ngIf="!$last"></div>
          </ng-container>
          } @if (ensembleClassDetail.assignedInstruments!.length > 1) {
          <div class="remaining-instrument-available-count" [matTooltip]="getInstrumentNames(ensembleClassDetail.assignedInstruments)">
            {{ ensembleClassDetail.assignedInstruments!.length - 1 }}+
          </div>
          }
        </div>
        <div class="dot"></div>
        <div class="student-content">
          <img [src]="constants.staticImages.icons.memberIcon" class="black-img" alt="" />
          <div class="location">
            Client Capacity
            <span class="primary-color ms-1">{{ ensembleClassDetail.noOfEnrolledStudents }}/{{ ensembleClassDetail.studentCapacity }}</span>
          </div>
        </div>
      </div>
    </div>
    <button
      *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
      mat-raised-button
      color="primary"
      class="mat-primary-btn"
      type="button"
      [appLoader]="showBtnLoaderId === ensembleClassDetail.id"
      (click)="openPayment(ensembleClassDetail)"
    >
      Enroll
    </button>
  </div>

  <div *ngIf="$index < ensembleClassDetails?.length! - 1" class="dotted-divider"></div>
  }
</ng-template>

<ng-template #showPaymentTemp>
  <app-payment-methods screen="available-ensemble-list" [isPaymentFailed]="isRePayment" [showDefaultPaymentBtn]="false" (closeSideNav)="onCloseSideNav()"></app-payment-methods>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-wrapper">
    <h4>No Ensemble Class Available.</h4>
  </div>
</ng-template>
