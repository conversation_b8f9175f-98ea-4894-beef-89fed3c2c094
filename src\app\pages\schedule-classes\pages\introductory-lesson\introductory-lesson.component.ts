import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { Instrument } from 'src/app/request-information/models';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { takeUntil } from 'rxjs';
import { CBGetResponse, CBResponse } from 'src/app/shared/models';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { AuthService } from 'src/app/auth/services';
import { AddSchedule, ClassTypes, LessonTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { AvailableIntroductoryLessonComponent } from './pages/available-introductory-lesson/available-introductory-lesson.component';
import { MatSidenav, MatSidenavModule } from '@angular/material/sidenav';
import { InstructorListComponent } from '../../../../schedule-introductory-lesson/pages/instructor-list/instructor-list.component';
import { BookIntroductoryLessonComponent } from './pages/book-introductory-lesson/book-introductory-lesson.component';
import {
  AddIntroductoryLessonForm,
  CardDetailsResponse,
  InstructorBioDetail,
  PaymentParams,
  RePaymentParams,
  SelectedLessonInfo
} from './models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { RevenueCategory } from 'src/app/pages/settings/pages/revenue-categories/models';
import { ActivatedRoute, Router } from '@angular/router';
import moment from 'moment';
import { PassesService } from 'src/app/pages/settings/pages/passes/services';
import { PaymentService } from '../../services';
import { InstrumentsService } from 'src/app/request-information/services';
import { CommonUtils } from 'src/app/shared/utils';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    FormsModule,
    SharedModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    MatSidenavModule
  ],
  COMPONENTS: [InstructorListComponent, AvailableIntroductoryLessonComponent, BookIntroductoryLessonComponent]
};

@Component({
  selector: 'app-introductory-lesson',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './introductory-lesson.component.html',
  styleUrl: './introductory-lesson.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IntroductoryLessonComponent extends BaseComponent implements OnInit {
  @Input() setBasicInfoFilled!: boolean;
  @Input() scheduleInfo!: SelectedLessonInfo;
  @Input() isScheduleMakeUpLesson!: boolean;

  schedulerInfoForm!: FormGroup<AddIntroductoryLessonForm>;
  slotOrStaffDetails!: InstructorBioDetail;
  instruments!: Array<Instrument>;
  locations!: Array<SchoolLocations>;
  revenueCategories!: Array<RevenueCategory>;
  rePaymentParams!: RePaymentParams | null;

  lessonType = LessonTypes;
  classType = ClassTypes;
  isInstructorsSideNavOpen = false;
  isBookYourLessonSideNavOpen = false;
  isFromVisit = false;
  isPassReschedule = false;
  selectedInstructorsId: Array<number> = [];
  selectedInstructorsIdOriginal: Array<number> = [];
  isAddressIncomplete = false;

  @ViewChild('instructorSideNav') instructorSideNav!: MatSidenav;
  @ViewChild(AvailableIntroductoryLessonComponent)
  availableIntroductoryLessonComponent!: AvailableIntroductoryLessonComponent;
  @ViewChild(BookIntroductoryLessonComponent) bookIntroductoryLessonComponent!: BookIntroductoryLessonComponent;

  constructor(
    private readonly commonService: CommonService,
    private readonly cdr: ChangeDetectorRef,
    private readonly authService: AuthService,
    private readonly toasterService: AppToasterService,
    protected readonly schedulerService: SchedulerService,
    private readonly datePipe: DatePipe,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
    private readonly passesService: PassesService,
    private readonly paymentService: PaymentService,
    private readonly instrumentsService: InstrumentsService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initSchedulerInfoForm();
    this.getCurrentUserDetails();
    this.getLocations();
    this.getRevenueCategories();
    this.getSegment();
  }

  getSegment(): void {
    this.activatedRoute.url.subscribe((params: any) => {
      this.isFromVisit = params[0].path === 'visits';
    });

    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params?.isReschedule) {
        this.isPassReschedule = true;
      }
    });
  }

  initSchedulerInfoForm(): void {
    this.schedulerInfoForm = new FormGroup<AddIntroductoryLessonForm>({
      isSpecialNeedsLesson: new FormControl(false, { nonNullable: true }),
      childAge: new FormControl(this.scheduleInfo?.childAge ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      lessonType: new FormControl(this.scheduleInfo?.lessonType ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      skillType: new FormControl(this.scheduleInfo?.skillType ?? '', { nonNullable: true }),
      instrumentId: new FormControl(this.scheduleInfo?.instrumentId ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      locationId: new FormControl(this.scheduleInfo?.locationId ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      studentId: new FormControl(this.scheduleInfo?.studentDetails![0].studentId ?? this.currentUser?.dependentId ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      classType: new FormControl(this.classType.INTRODUCTORY, { nonNullable: true }),
      revenueCategoryId: new FormControl(undefined, { nonNullable: true })
    });
  }

  getCurrentUserDetails(): void {
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.initSchedulerInfoForm();
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(ageGroup: number): void {
    if (!ageGroup) return;
    this.schedulerInfoForm.controls.instrumentId.reset();
    this.instrumentsService
      .getList<CBResponse<Instrument>>(`${API_URL.crud.getAll}?AgeGroup=${ageGroup}&IsHiddenFromClient=true`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getRevenueCategories(): void {
    this.commonService
      .getRevenueCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RevenueCategory>) => {
          this.revenueCategories = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  onLessonTypeChange(lessonType: number | undefined): void {
    const categoryName =
      lessonType === LessonTypes.IN_PERSON
        ? this.constants.revenueCategories.inPersonRevenue
        : this.constants.revenueCategories.remoteRevenue;

    const id = this.revenueCategories.find(item => item.category.categoryName === categoryName)?.category.id!;
    this.setFormControlValue('revenueCategoryId', id);
  }

  setFormControlValue(controlName: string, value: string | number | boolean): void {
    const control = this.schedulerInfoForm.get(controlName);
    (this.schedulerInfoForm.controls as any)[controlName].setValue(value);

    if (controlName === 'childAge') {
      this.handleAgeChange(control?.value);
    }

    if (controlName === 'lessonType') {
      this.onLessonTypeChange(control?.value);
    }
  }

  handleAgeChange(childAge: number): void {
    const isSpecialAge = [this.constants.childAgeValues.nineToSeven, this.constants.childAgeValues.eighteenPlus].includes(childAge);
    this.setRequiredBasedOnCondition('skillType', isSpecialAge);
    this.schedulerInfoForm.controls.skillType.reset();
    this.schedulerInfoForm.controls.lessonType.reset();
    if (!isSpecialAge) {
      this.schedulerInfoForm.controls.skillType.setValue('Beginner');
    }
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean): void {
    const control = this.schedulerInfoForm.get(controlName);
    if (required) {
      control?.setValidators([Validators.required]);
    } else {
      control?.clearValidators();
    }
    control?.updateValueAndValidity();
  }

  getSchedulerInfoFormValue(): void {
    this.paymentService.setUserPayment(null);
    if (this.schedulerInfoForm.invalid) {
      this.schedulerInfoForm.markAllAsTouched();
      return;
    }
    this.schedulerInfoForm.markAsUntouched();
    this.scheduleInfo = this.schedulerInfoForm.getRawValue() as SelectedLessonInfo;
    this.setBasicInfoFilled = true;
  }

  formatTime(originalDateTime?: string, scheduleDate?: string): string {
    const time = moment(originalDateTime).format(this.constants.dateFormats.HH_mm_ss);
    if (scheduleDate) {
      return moment(scheduleDate).format(this.constants.dateFormats.yyyy_MM_DD) + `T${time}`;
    }

    return CommonUtils.combineDateAndTime(scheduleDate ?? '', time);
  }

  isAddressIncompleteFn(): void {
    this.paymentService.isAddressIncomplete$.pipe(takeUntil(this.destroy$)).subscribe(isIncomplete => {
       this.isAddressIncomplete = isIncomplete;
       this.cdr.markForCheck();
    });
  }

  get getScheduleInfoDetails(): AddSchedule {
    const revenueCategoryId = this.revenueCategories.find(
      item => item.category.categoryName === this.constants.revenueCategories.inPersonRevenue
    )?.category.id!;

    return {
      id: undefined,
      classType: this.isScheduleMakeUpLesson ? this.classType.MAKE_UP : this.classType.INTRODUCTORY,
      studentId: this.currentUser?.dependentDetails?.length
        ? this.schedulerInfoForm.getRawValue().studentId
        : this.currentUser?.dependentId,
      lessonType: this.scheduleInfo.lessonType,
      instrumentId: this.scheduleInfo.instrumentId,
      locationId: this.scheduleInfo.locationId,
      skillType: this.scheduleInfo.skillType,
      daysOfSchedule: [],
      scheduleDate: CommonUtils.combineDateAndTime(
        this.slotOrStaffDetails?.slotDetails?.scheduleDate ?? '',
        this.slotOrStaffDetails?.slotDetails?.startTime ?? ''
      ),
      scheduleStartTime: CommonUtils.combineDateAndTime(
        this.slotOrStaffDetails?.slotDetails?.scheduleDate ?? '',
        this.slotOrStaffDetails?.slotDetails?.startTime ?? ''
      ),
      scheduleEndTime: CommonUtils.combineDateAndTime(
        this.slotOrStaffDetails?.slotDetails?.scheduleDate ?? '',
        this.slotOrStaffDetails?.slotDetails?.endTime ?? ''
      ),
      instructorId: this.slotOrStaffDetails?.slotDetails?.instructorId,
      revenueCategoryId: revenueCategoryId,
      isDraftSchedule: !this.isScheduleMakeUpLesson,
      roomId: this.scheduleInfo.roomId
    };
  }

  onConfirmAppointments(cardDetails = null): void {
    this.schedulerService
      .add(this.getScheduleInfoDetails, API_URL.crud.create)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<{ id: number }>) => {
          if (this.scheduleInfo.passId) {
            this.updatePassStatus();
          }
          if (!cardDetails) {
            this.bookIntroductoryLessonComponent.onCloseModal();
          }
          this.initPaymentProcess(cardDetails, res.result?.id, this.getScheduleInfoDetails.studentId);
          this.isScheduleMakeUpLesson ? this.navigateBack() : this.redirectToMainPage();
          if (this.isScheduleMakeUpLesson) {
            this.toasterService.success(this.constants.successMessages.addedSuccessfully.replace('{item}', 'Schedule'));
          }
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  initPaymentProcess(cardDetails: CardDetailsResponse | null, scheduleId?: number, studentId?: number) {
    if (!cardDetails) {
      return;
    }
    this.isAddressIncompleteFn();
    if (this.isAddressIncomplete) {
     this.toasterService.error(this.constants.errorMessages.addressIncomplete);
     return;
    }
    this.paymentService.showBtnLoader(true);
    if (cardDetails.isUsingSavedCard === false) {
      this.paymentService
        .add(this.getPaymentParams(cardDetails, scheduleId, studentId), API_URL.payment.NMIPayment)
        .subscribe({
          next: (res) => {
            this.bookIntroductoryLessonComponent?.onCloseModal();
            this.toasterService.success(this.constants.successMessages.paymentSuccess);
            this.rePaymentParams = null;
            this.bookIntroductoryLessonComponent?.setIsRePaymentDone(false);
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          },
          error: () => {
            this.rePaymentParams = { scheduleId, studentId };
            this.bookIntroductoryLessonComponent?.setIsRePaymentDone(true);
            this.bookIntroductoryLessonComponent?.setIsPaymentDone(false);
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          }
        });
    } else {
      this.paymentService
        .add(this.getCardDetails(cardDetails, scheduleId, studentId), API_URL.payment.paymentUsingSavedCard)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.bookIntroductoryLessonComponent?.onCloseModal();
            this.toasterService.success(this.constants.successMessages.paymentSuccess);
            this.rePaymentParams = null;
            this.bookIntroductoryLessonComponent?.setIsRePaymentDone(false);
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          },
          error: () => {
            this.showBtnLoader = false;
            this.rePaymentParams = { scheduleId, studentId };
            this.bookIntroductoryLessonComponent?.setIsRePaymentDone(true);
            this.bookIntroductoryLessonComponent?.setIsPaymentDone(false);
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          }
        });
    }
  }

  getCardDetails(cardDetails: CardDetailsResponse, scheduleId?: number, studentId?: number): PaymentParams {
    return {
      userId:this.currentUser?.userId,
      dependentInformationId: studentId,
      classType: this.getScheduleInfoDetails.classType,
      scheduleId: scheduleId,
      amount: this.scheduleInfo.price?? this.constants.defaultAmountPayableForIntroductoryClasses,
      paidDate: new Date(),
      customerVaultId: cardDetails.customerVaultId,
      transactionType: cardDetails.transactionType,
      paidAmount: cardDetails.paidAmount,
      totalAmount:cardDetails.totalAmount,
      discountedAmount: cardDetails.discountedAmount
    };
  }

  getPaymentParams(cardDetails: CardDetailsResponse, scheduleId?: number, studentId?: number): PaymentParams {
    return {
      dependentInformationId: studentId,
      classType: this.getScheduleInfoDetails.classType,
      scheduleId: scheduleId,
      amount: this.scheduleInfo.price?? this.constants.defaultAmountPayableForIntroductoryClasses,
      ccNum: cardDetails.number,
      ccExpiry: cardDetails.expiry,
      ccType: cardDetails.type,
      token: cardDetails.token,
      isSaveCard: cardDetails.isSaveCard,
      paidDate: new Date(),
      address: cardDetails.address,
      city: cardDetails.city,
      state: cardDetails.state,
      zip: cardDetails.zip,
      firstName: cardDetails.firstName,
      lastName: cardDetails.lastName,
      customerVaultId: cardDetails.customerVaultId,
      transactionType: cardDetails.transactionType,
      paidAmount: cardDetails.paidAmount,
      totalAmount:cardDetails.totalAmount,
      discountedAmount: cardDetails.discountedAmount
    };
  }

  onRescheduleAppointment(): void {
    this.schedulerService
      .update(
        {
          ...this.getScheduleInfoDetails,
          id: this.scheduleInfo.id,
          scheduleStartDate: this.getScheduleInfoDetails.scheduleStartTime,
          scheduleEndDate: this.getScheduleInfoDetails.scheduleEndTime
        },
        API_URL.crud.update
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.bookIntroductoryLessonComponent.onCloseModal();
          this.navigateBack();
          this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Schedule'));
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  updatePassStatus(): void {
    this.passesService.update({ id: this.scheduleInfo.passId }, API_URL.passes.updateStatus).pipe(takeUntil(this.destroy$)).subscribe();
  }

  resetInstructorSideNav(): void {
    this.selectedInstructorsId = this.selectedInstructorsIdOriginal;
  }

  setSelectedInstructorId(event: Array<number>): void {
    this.selectedInstructorsId = event;
    this.availableIntroductoryLessonComponent?.getIntroductoryLessonDetails(this.selectedInstructorsId);
    this.cdr.detectChanges();
  }

  setScheduleAppointmentsDetails(event: InstructorBioDetail): void {
    this.slotOrStaffDetails = event;
    const scheduleDetails = {
      startDate: this.slotOrStaffDetails?.dateRange?.startDate,
      endDate: this.slotOrStaffDetails?.dateRange?.endDate,
      scheduleDate: this.slotOrStaffDetails?.slotDetails?.scheduleDate,
      instructorName: this.slotOrStaffDetails?.slotDetails?.instructorName,
      scheduleStartTime: this.slotOrStaffDetails?.slotDetails?.startTime,
      scheduleEndTime: this.slotOrStaffDetails?.slotDetails?.endTime,
      classType: this.classType.INTRODUCTORY,
      roomId: this.slotOrStaffDetails?.slotDetails?.roomId
    };

    this.scheduleInfo = this.isScheduleMakeUpLesson
      ? { ...this.scheduleInfo, ...scheduleDetails }
      : { ...this.schedulerInfoForm.getRawValue(), ...scheduleDetails };
  }

  showAppointmentDetails(): void {
    if (this.slotOrStaffDetails) {
      this.slotOrStaffDetails.showStaffDetails = false;
    }
  }

  toggleInstructorSideNav(isOpen: boolean, isBook = false): void {
    this.isInstructorsSideNavOpen = isOpen;
    this.isBookYourLessonSideNavOpen = isBook;
    this.cdr.detectChanges();
    if (isOpen) {
      this.selectedInstructorsIdOriginal = JSON.parse(JSON.stringify(this.selectedInstructorsId));
    }
    if (isBook && !this.slotOrStaffDetails?.slotDetails) {
      this.toasterService.error(this.constants.errorMessages.selectionPending.replace('{item}', 'an Appointment'));
    } 
  }

  redirectToMainPage(): void {
    this.schedulerInfoForm.reset();
    this.setBasicInfoFilled = false;
  }

  navigateBack(): void {
    this.setBasicInfoFilled = false;
    if (this.isFromVisit) {
      this.router.navigate([this.path.visits.root], {
        queryParams: {
          activeTab: 'Upcoming'
        }
      });
      return;
    }
    if (this.isScheduleMakeUpLesson) {
      this.router.navigate([this.path.plansAndPasses.root], {
        queryParams: {
          activeTab: 'Pass'
        }
      });
    }
  }

  getInstrumentNameFromValue(value: number | undefined): string {
    return this.instruments?.find(name => name.instrumentDetail.id === value)?.instrumentDetail.name || '';
  }

  getLocationNameFromValue(value: number | undefined): string {
    return this.locations?.find(name => name.schoolLocations.id === value)?.schoolLocations.locationName || '';
  }

  getDependentNameFromValue(value: number | undefined): string {
    if (this.currentUser?.dependentDetails && this.currentUser?.dependentDetails?.length) {
      return (
        `${this.currentUser?.dependentDetails.find(name => name.id === value)?.firstName || ''} ${
          this.currentUser?.dependentDetails.find(name => name.id === value)?.lastName || ''
        }` || ''
      );
    } else {
      return `${this.currentUser?.firstName || ''} ${this.currentUser?.lastName || ''}` || '';
    }
  }
}
