import { AddEnsembleClassComponent } from './pages/add-ensemble-class/add-ensemble-class.component';
import { ViewEnsembleClassComponent } from './pages/view-ensemble-class/view-ensemble-class.component';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule, DatePipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { CBResponse, IdNameModel, MatDialogRes } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { CommonUtils } from 'src/app/shared/utils';
import { All } from '../plan/models/plan-summary.model';
import { EnsembleClassesService } from 'src/app/pages/schedule-classes/pages/ensemble-class/services';
import { LessonTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { Instrument } from 'src/app/request-information/models';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { NgxPaginationModule } from 'ngx-pagination';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { ActivatedRoute, Router } from '@angular/router';
import {
  DateFilterTypeEnum,
  EnsembleClassScheduleSummaryInfo,
  EnsembleClassFilters
} from 'src/app/pages/schedule-classes/pages/ensemble-class/models/ensemble-class.model';
import { UpdateEnsembleClassComponent } from './pages/update-ensemble-class/update-ensemble-class.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { EnrollStudentInEnsembleComponent } from './pages/enroll-student-in-ensemble/enroll-student-in-ensemble.component';
import { MultiSelectComponent } from 'src/app/shared/components/multi-select/multi-select.component';
import { DateUtils } from 'src/app/shared/utils/date.utils';

const DEPENDENCIES = {
  MODULES: [
    SharedModule,
    MatSidenavModule,
    MatSelectModule,
    MatButtonModule,
    CommonModule,
    FormsModule,
    MatInputModule,
    MatIconModule,
    NgxPaginationModule,
    MatTooltipModule
  ],
  COMPONENTS: [AddEnsembleClassComponent, ViewEnsembleClassComponent, UpdateEnsembleClassComponent, EnrollStudentInEnsembleComponent, MultiSelectComponent]
};

@Component({
  selector: 'app-ensemble-class-creation',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './ensemble-class-creation.component.html',
  styleUrl: './ensemble-class-creation.component.scss'
})
export class EnsembleClassCreationComponent extends BaseComponent implements OnInit {
  isAddEsembleClassSideNavOpen = false;
  isViewEnsembleClassOpen = false;
  isEditFromView = false;
  isCloneSideNavOpen = false;
  isEnrolSideNavOpen = false;

  selectedEnsembleId!: number | null;
  selectedEnsembleClass!: EnsembleClassScheduleSummaryInfo | null;
  ensembleClassDetails!: Array<EnsembleClassScheduleSummaryInfo>;
  instruments!: Array<Instrument>;
  locations!: Array<SchoolLocations>;

  pageSize = this.paginationConfig.itemsPerPage;
  currentPage = this.paginationConfig.pageNumber;
  totalCount!: number;
  lessonType = LessonTypes;
  all = All;
  filters: EnsembleClassFilters = {
    instrumentIdFilter: {
      id: 1,
      defaultPlaceholder: 'All Instruments',
      placeholder: 'All Instruments',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    locationIdFilter: {
      id: 2,
      defaultPlaceholder: 'All Locations',
      placeholder: 'All Locations',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    ageGroupFilter: 0,
    currentDateFilter: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss)
  };

  pageTabOptions = { ONGOING: 'Ongoing Class', UPCOMING: 'Upcoming Class' };
  selectedTabOption = this.pageTabOptions.ONGOING;

  constructor(
    private readonly ensembleClassService: EnsembleClassesService,
    private readonly commonService: CommonService,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService,
    private readonly cdr: ChangeDetectorRef,
    protected readonly schedulerService: SchedulerService,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
    private readonly datePipe: DatePipe
  ) {
    super();
  }

  ngOnInit(): void {
    this.setActiveTabFromQueryParams();
    this.getInstruments();
    this.getLocations();
  }

  toggleAddEditSideNav(isOpen: boolean, isView: boolean, ensembleClassId: number | null, isEditFromView = false): void {
    this.isAddEsembleClassSideNavOpen = isOpen;
    this.isViewEnsembleClassOpen = isView;
    this.selectedEnsembleId = ensembleClassId;
    this.isEditFromView = isEditFromView;
  }

  toggleEnrollStudentSideNav(isOpen: boolean, selectedClass: EnsembleClassScheduleSummaryInfo | null): void {
    this.isEnrolSideNavOpen = isOpen;
    this.selectedEnsembleClass = selectedClass;
  }

  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (Object.keys(params).length) {
        this.selectedTabOption = params.activeTab;
        this.getEnsembleClassDetail(this.currentPage, this.pageSize);
        return;
      }
      this.setActiveTabOption(this.pageTabOptions.ONGOING);
    });
  }

  setActiveTabOption(tabName: string): void {
    this.selectedTabOption = tabName;
    this.router.navigate([this.path.settings.root, this.path.settings.ensembleClass], {
      queryParams: {
        activeTab: tabName
      }
    });
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.getEnsembleClassDetail(this.currentPage, this.pageSize);
  }

  getFilterParams(currentPage: number, pageSize: number) {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      InstrumentIdFilter: [...this.filters.instrumentIdFilter.value],
      LocationIdFilter: [...this.filters.locationIdFilter.value],
      AgeEnsembleFilter: this.filters.ageGroupFilter,
      scheduleStartDate: DateUtils.getUtcRangeForLocalDate(this.filters.currentDateFilter ?? '').startUtc,
      scheduleEndDate: DateUtils.getUtcRangeForLocalDate(this.filters.currentDateFilter ?? '').endUtc,
      DateFilterType: this.getDateTypeFilter(),
      Page: currentPage,
      PageSize: pageSize
    });
  }

  getDateTypeFilter(): number {
    switch (this.selectedTabOption) {
      case this.pageTabOptions.UPCOMING:
        return DateFilterTypeEnum.UPCOMING_CLASSES;
      default:
        return DateFilterTypeEnum.ONGOING_CLASS;
    }
  }

  getEnsembleClassDetail(currentPage: number, pageSize: number): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();
    this.ensembleClassService
      .add(this.getFilterParams(currentPage, pageSize), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<EnsembleClassScheduleSummaryInfo>) => {
          this.ensembleClassDetails = res.result.items.map(item => ({
            ...item,
            scheduleStartDate: DateUtils.toLocal(item.scheduleStartDate, 'yyyy-MM-DDTHH:mm:ss'),
            scheduleEndDate: DateUtils.toLocal(item.scheduleEndDate, 'yyyy-MM-DDTHH:mm:ss'),
            enrollLastDate: DateUtils.toLocal(item.enrollLastDate, 'yyyy-MM-DDTHH:mm:ss'),
            scheduleStartTime: DateUtils.toLocal(item.scheduleStartTime, 'yyyy-MM-DDTHH:mm:ss'),
            scheduleEndTime: DateUtils.toLocal(item.scheduleEndTime, 'yyyy-MM-DDTHH:mm:ss')
          }));
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.filters.instrumentIdFilter.options = res.result.items.map(instrument => ({
            id: instrument.instrumentDetail.id,
            name: instrument.instrumentDetail.name
          }));
          this.filters.instrumentIdFilter.totalCount = this.instruments.length;
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.filters.locationIdFilter.options = res.result.items.map(location => ({
            id: location.schoolLocations.id,
            name: location.schoolLocations.locationName
          }));
          this.filters.locationIdFilter.totalCount = this.locations.length;
          this.cdr.detectChanges();
        }
      });
  }

  deleteEnsembleClassConfirmation(id: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Ensemble Class`,
        message: `Are you sure you want to delete this ensemble class?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.deleteEnsembleClass(id);
      }
    });
  }

  deleteEnsembleClass(id: number): void {
    this.ensembleClassService
      .delete(id, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Ensemble Class'));
          this.getEnsembleClassDetail((this.currentPage = 1), this.pageSize);
        }
      });
  }

  onCloseSideNav(): void {
    this.isAddEsembleClassSideNavOpen = false;
  }

  keepOriginalOrder = () => 0;
}
