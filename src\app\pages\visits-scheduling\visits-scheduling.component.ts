import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule, DatePipe } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { SchedulerService } from '../scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { CBResponse } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { CurrentUserScheduleLessonDetail, ScheduleStatus, VisitsFilters } from './models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { SharedModule } from 'src/app/shared/shared.module';
import { Debounce } from 'src/app/shared/decorators';
import { MatSelectModule } from '@angular/material/select';
import { ClassTypes } from '../scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { CancelScheduleComponent } from './pages/cancel-schedule/cancel-schedule.component';
import { CommonUtils } from 'src/app/shared/utils';
import { NgxPaginationModule } from 'ngx-pagination';
import { EnumToKeyValuePipe } from 'src/app/shared/pipe';
import { AccountManagerSchedulePaymentComponent } from '../members/pages/students/pages/account-manager-schedule-payment/account-manager-schedule-payment.component';
import { CardDetailsResponse, PaymentParams, RePaymentParams } from '../schedule-classes/pages/introductory-lesson/models';
import { PaymentService } from '../schedule-classes/services';
import { AppToasterService } from 'src/app/shared/services';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AssignedInstructors } from '../schedule-classes/pages/ensemble-class/models';
import { AuthService } from 'src/app/auth/services';
import { DateUtils } from 'src/app/shared/utils/date.utils';

const DEPENDENCIES = {
  MODULES: [
    SharedModule,
    MatSidenavModule,
    MatSelectModule,
    MatButtonModule,
    CommonModule,
    MatFormFieldModule,
    MatIconModule,
    FormsModule,
    MatInputModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    MatTooltipModule
  ],
  COMPONENTS: [CancelScheduleComponent, AccountManagerSchedulePaymentComponent],
  PIPES: [EnumToKeyValuePipe]
};

@Component({
  selector: 'app-visits-scheduling',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  templateUrl: './visits-scheduling.component.html',
  styleUrl: './visits-scheduling.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class VisitsSchedulingComponent extends BaseComponent implements OnInit {
  pageTabOptions = { UPCOMING: 'Upcoming', PAST: 'Past' };
  selectedTabOption = this.pageTabOptions.UPCOMING;
  isCancelSideNavOpen = false;
  isPaymentSideNavOpen = false;
  rePaymentParams!: RePaymentParams | null;
  schedulingDetails!: CurrentUserScheduleLessonDetail[];
  CLASS_TYPE = ClassTypes;
  ScheduleStatus = ScheduleStatus;
  selectedScheduleDetails!: CurrentUserScheduleLessonDetail | null;
  totalCount!: number;
  pageSize = this.paginationConfig.itemsPerPage;
  currentPage = this.paginationConfig.pageNumber;
  isAddressIncomplete = false;
  date = new Date().toISOString();
  filters: VisitsFilters = {
    isPastSchedule: false,
    currentDate: this.date,
    statusFilter: 0,
    searchFilter: null
  };

  @ViewChild(AccountManagerSchedulePaymentComponent)
  accountManagerSchedulePaymentComponent!: AccountManagerSchedulePaymentComponent;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    protected readonly schedulerService: SchedulerService,
    private readonly paymentService: PaymentService,
    private readonly cdr: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly authService: AuthService
  ) {
    super();
  }

  ngOnInit(): void {
    this.setActiveTabFromQueryParams();
    this.getCurrentUserDetails();
  }

  getCurrentUserDetails(): void {
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.cdr.detectChanges();
        }
      });
  }
  
  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (Object.keys(params).length) {
        this.selectedTabOption = params.activeTab;
        this.getScheduleLessonDetail(this.currentPage, this.pageSize);
        return;
      }
      this.setActiveTabOption(this.pageTabOptions.UPCOMING);
    });
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.getScheduleLessonDetail(this.currentPage, this.pageSize);
  }

  getFilterParams(): VisitsFilters {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      IsPastSchedule: this.selectedTabOption === this.pageTabOptions.UPCOMING ? false : true,
      CurrentDate: this.filters.currentDate,
      StatusFilter: this.filters.statusFilter,
      SearchFilter: this.filters.searchFilter
    });
  }

  getScheduleLessonDetail(currentPage: number, pageSize: number): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();
    this.schedulerService
      .getListWithFiltersWithPagination<CBResponse<CurrentUserScheduleLessonDetail>>(
        this.getFilterParams(),
        currentPage,
        pageSize,
        `${API_URL.scheduleLessonDetails.getCurrentUserScheduleLessonDetail}`,
        false
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<CurrentUserScheduleLessonDetail>) => {
          this.schedulingDetails = res.result.items.map(item => ({
            ...item,
            start: DateUtils.toLocal(item.start),
            end: DateUtils.toLocal(item.end),
            scheduleDate: DateUtils.toLocal(item.scheduleDate)
          }));
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  setScheduleInfo(data: CurrentUserScheduleLessonDetail): void {
    this.paymentService.setUserPayment(null);
    this.selectedScheduleDetails = data;
    this.selectedScheduleDetails.start = DateUtils.toUTC(this.selectedScheduleDetails.start, 'yyyy-MM-DDTHH:mm:ss');
    this.selectedScheduleDetails.end = DateUtils.toUTC(this.selectedScheduleDetails.end, 'yyyy-MM-DDTHH:mm:ss');
    this.selectedScheduleDetails.scheduleDate = DateUtils.toUTC(this.selectedScheduleDetails.scheduleDate, 'yyyy-MM-DDTHH:mm:ss');
    const duration = this.getTimeDiff(this.selectedScheduleDetails?.start!, this.selectedScheduleDetails?.end!);

    if (data.classType === ClassTypes.MAKE_UP) {
      this.router.navigate([this.path.visits.root, this.path.scheduleMakeUpLesson], {
        queryParams: { scheduleId: this.selectedScheduleDetails?.id, d: duration, isReschedule: true }
      });
    } else if (data.classType === ClassTypes.INTRODUCTORY && !data?.isPaid) {
      this.isPaymentSideNavOpen = true;
    } else {
      this.isCancelSideNavOpen = true;
    }
  }

  getInstructorNames(array: AssignedInstructors[]): string {
    return array
      .slice(1)
      .map(item => item.instructorName)
      .join(', ');
  }

  isAddressIncompleteFn(): void {
    this.paymentService.isAddressIncomplete$.pipe(takeUntil(this.destroy$)).subscribe(isIncomplete => {
      this.isAddressIncomplete = isIncomplete;
      this.cdr.markForCheck();
    });
  }

  initPaymentProcess(cardDetails: CardDetailsResponse | null, scheduleId?: number, studentId?: number) {
    scheduleId = scheduleId ?? this.selectedScheduleDetails?.id;
    studentId = studentId ?? this.selectedScheduleDetails?.studentId;
    if (!cardDetails) {
      return;
    }
    this.isAddressIncompleteFn();
    if (this.isAddressIncomplete) {
      this.toasterService.error(this.constants.errorMessages.addressIncomplete);
      return;
    }
    this.paymentService.showBtnLoader(true);
    if (cardDetails.isUsingSavedCard === false) {
      this.paymentService
        .add(this.getPaymentParams(cardDetails, scheduleId, studentId), API_URL.payment.NMIPayment)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: res => {
            this.refreshScheduleData();
            this.accountManagerSchedulePaymentComponent.onCloseModal();
            this.toasterService.success(this.constants.successMessages.paymentSuccess);
            this.rePaymentParams = null;
            this.accountManagerSchedulePaymentComponent.setIsRePaymentDone(false);
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          },
          error: () => {
            this.rePaymentParams = { scheduleId, studentId };
            this.accountManagerSchedulePaymentComponent.setIsRePaymentDone(true);
            this.accountManagerSchedulePaymentComponent.setIsPaymentDone(false);
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          }
        });
    } else {
      this.paymentService
        .add(this.getCardDetails(cardDetails, scheduleId, studentId), API_URL.payment.paymentUsingSavedCard)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.refreshScheduleData();
            this.accountManagerSchedulePaymentComponent.onCloseModal();
            this.toasterService.success(this.constants.successMessages.paymentSuccess);
            this.rePaymentParams = null;
            this.accountManagerSchedulePaymentComponent.setIsRePaymentDone(false);
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          },
          error: () => {
            this.rePaymentParams = { scheduleId, studentId };
            this.accountManagerSchedulePaymentComponent.setIsRePaymentDone(true);
            this.accountManagerSchedulePaymentComponent.setIsPaymentDone(false);
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          }
        });
    }
  }

  getCardDetails(cardDetails: CardDetailsResponse, scheduleId?: number, studentId?: number): PaymentParams {
    return {
      userId: this.currentUser?.userId,
      dependentInformationId: studentId,
      classType: ClassTypes.INTRODUCTORY,
      scheduleId: scheduleId,
      paidDate: new Date(),
      customerVaultId: cardDetails.customerVaultId,
      transactionType: cardDetails.transactionType,
      paidAmount: cardDetails.paidAmount,
      totalAmount: cardDetails.totalAmount,
      discountedAmount: cardDetails.discountedAmount
    };
  }

  getPaymentParams(cardDetails: CardDetailsResponse, scheduleId?: number, studentId?: number): PaymentParams {
    return {
      dependentInformationId: studentId,
      classType: ClassTypes.INTRODUCTORY,
      scheduleId: scheduleId,
      ccNum: cardDetails.number,
      ccExpiry: cardDetails.expiry,
      ccType: cardDetails.type,
      token: cardDetails.token,
      isSaveCard: cardDetails.isSaveCard,
      paidDate: new Date(),
      address: cardDetails.address,
      city: cardDetails.city,
      state: cardDetails.state,
      zip: cardDetails.zip,
      firstName: cardDetails.firstName,
      lastName: cardDetails.lastName,
      transactionType: cardDetails.transactionType,
      paidAmount: cardDetails.paidAmount,
      totalAmount: cardDetails.totalAmount,
      discountedAmount: cardDetails.discountedAmount
    };
  }

  getTimeDiff(start: string, end: string): number {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  onCloseSideNav(): void {
    this.isCancelSideNavOpen = false;
    this.selectedScheduleDetails = null;
  }

  refreshScheduleData(): void {
    this.isCancelSideNavOpen = false;
    this.getScheduleLessonDetail(this.currentPage, this.pageSize);
  }

  @Debounce(300)
  onSearchTermChanged(): void {
    this.currentPage = 1;
    this.getScheduleLessonDetail(this.currentPage, this.pageSize);
  }

  keepOriginalOrder = () => 0;

  setActiveTabOption(tabName: string): void {
    this.selectedTabOption = tabName;
    this.router.navigate([this.path.visits.root], {
      queryParams: {
        activeTab: tabName
      }
    });
  }
}
