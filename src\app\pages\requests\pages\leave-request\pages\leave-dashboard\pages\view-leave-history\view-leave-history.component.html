<div class="o-sidebar-wrapper">
    <div class="o-sidebar-header">
        <div class="title">Leave Request Details</div>
        <div class="action-btn-wrapper">
            <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button"
                (click)="closeViewSideNavFun()">
                Close
            </button>
        </div>
    </div>
    <div class="divider"></div>
    <div class="o-sidebar-body">
        <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : leaveDetails"></ng-container>
    </div>
</div>

<ng-template #leaveDetails>
    <div class="leave-details">
        <div class="d-flex">
            <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
            <div class="title">Leave Date</div>
        </div>
        <div class="content">
            <div>
            @if(selectedLeaveDetails?.leaveStartTime) {
               <div class="info"> {{ selectedLeaveDetails?.leaveStartDate | date }} <div class="dot d-inline-block"></div> {{ selectedLeaveDetails?.leaveStartTime | date: 'shortTime' }} - {{ selectedLeaveDetails?.leaveEndTime | date: 'shortTime' }} <span class="text-gray ms-1">({{ getHoursInRange(selectedLeaveDetails!.leaveStartTime, selectedLeaveDetails!.leaveEndTime) }} hours)</span></div>
            }
            @else {
                <div class="info"> {{ selectedLeaveDetails?.leaveStartDate | date }} - {{ selectedLeaveDetails?.leaveEndDate | date }} <span class="text-gray ms-1">({{ getDayInRange(selectedLeaveDetails!.leaveStartDate, selectedLeaveDetails!.leaveEndDate) }} day)</span></div>
              }
              <div class="requested-on">Requested on <span>{{ selectedLeaveDetails?.requestDate | date }}</span> </div>
            </div>
        </div>
    </div>
    <div class="leave-details">
        <div class="d-flex">
            <img [src]="constants.staticImages.icons.checkCircle" alt="" />
            <div class="title">Status</div>
        </div>
        <div class="content text-gray">
            <span
                [ngClass]="{'primary-color': selectedLeaveDetails?.status === leaveStatus.APPROVED, 'text-red': selectedLeaveDetails?.status === leaveStatus.REJECTED }">{{
                leaveStatus[selectedLeaveDetails!.status] | titlecase }}</span>
                <span class="ms-1">by {{ selectedLeaveDetails?.approvedBy |
            titlecase }} at {{ selectedLeaveDetails?.approveDate | date }}</span> 
        </div>
    </div>
    <div class="leave-details">
        <div class="d-flex">
            <img [src]="constants.staticImages.icons.repeatType" alt="" />
            <div class="title">Leave Type</div>
        </div>
        <div class="content">{{ leaveTypes[selectedLeaveDetails!.leaveType] | titlecase }} Leave</div>
    </div>
    <div class="leave-details">
        <div class="d-flex">
            <img [src]="constants.staticImages.icons.questionnaire" alt="" />
            <div class="title">Leave Note</div>
        </div>
        <div class="content">{{ selectedLeaveDetails?.reason | titlecase }}</div>
    </div>
    @if(selectedLeaveDetails?.status === leaveStatus.REJECTED) {
    <div class="leave-details">
        <div class="d-flex">
            <img [src]="constants.staticImages.icons.questionnaire" alt="" />
            <div class="title">Reason</div>
        </div>
        <div class="content">{{ selectedLeaveDetails?.remark | titlecase }}</div>
    </div>
    }
</ng-template>

<ng-template #showLoader>
    <div class="page-loader-wrapper">
        <app-content-loader></app-content-loader>
    </div>
</ng-template>