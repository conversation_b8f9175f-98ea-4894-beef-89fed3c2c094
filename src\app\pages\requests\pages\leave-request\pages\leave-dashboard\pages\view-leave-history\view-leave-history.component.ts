import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { LeaveRequestDetails, LeaveStatus, LeaveType } from '../../../../models';
import moment from 'moment';
import { CommonUtils } from 'src/app/shared/utils';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    MatButtonModule,
    SharedModule
  ]
};

@Component({
  selector: 'app-view-leave-history',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './view-leave-history.component.html',
  styleUrl: './view-leave-history.component.scss'
})
export class ViewLeaveHistoryComponent extends BaseComponent implements OnChanges {
  @Input() selectedLeaveDetails!: LeaveRequestDetails | null;

  leaveTypes = LeaveType;
  leaveStatus = LeaveStatus;

  @Output() closeViewSideNav = new EventEmitter<void>();

  constructor() {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedLeaveDetails']?.currentValue) {
      this.selectedLeaveDetails = changes['selectedLeaveDetails']?.currentValue;
    }
  }

  getDayInRange(startDate: string, scheduleDate: string): number {
    return moment(new Date(scheduleDate)).diff(new Date(startDate), 'days') + 1;
  }

  getHoursInRange(startTime: string, endTime: string): number {
    return CommonUtils.getHoursInRangeQuarter(startTime, endTime);
  }

  closeViewSideNavFun(): void {
    this.closeViewSideNav.emit();
  }
}
