import { CommonModule, DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SchedulerService, StudentPlanService } from '../../services';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import {
  ClassTypes,
  InstructorAvaibility,
  StudentPlans,
  SuggestedTimeSlot,
  UpdateScheduleFormGroup,
  ScheduleDetailsView
} from '../../models';
import { CommonUtils } from 'src/app/shared/utils';
import { AbstractControlOptions, FormArray, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { GroupClassesService } from 'src/app/pages/schedule-classes/pages/group-class/services';
import { GroupClassView } from 'src/app/pages/schedule-classes/pages/group-class/models/group-class.model';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { outOfRangeTimeValidator, timeRangeValidator } from 'src/app/shared/validators';
import { AppToasterService } from 'src/app/shared/services';
import { SummerCampScheduleService } from 'src/app/pages/settings/pages/summer-camp-creation/services';
import { AssignedInstructors } from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { MatTooltipModule } from '@angular/material/tooltip';
import { EnsembleClassesService } from 'src/app/pages/schedule-classes/pages/ensemble-class/services';
import { Debounce } from 'src/app/shared/decorators';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { DateUtils } from 'src/app/shared/utils/date.utils';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    CommonModule,
    SharedModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatSelectModule,
    NgxMaterialTimepickerModule,
    MatTooltipModule
  ],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-update-schedule',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  providers: [provideNativeDateAdapter()],
  templateUrl: './update-schedule.component.html',
  styleUrl: './update-schedule.component.scss'
})
export class UpdateScheduleComponent extends BaseComponent implements OnInit, AfterViewInit {
  @Input() selectedEvent!: ScheduleDetailsView;

  scheduleInfo!: ScheduleDetailsView | undefined;
  groupClassInfo!: GroupClassView | undefined;
  updateScheduleForm!: FormGroup<UpdateScheduleFormGroup>;
  assignedPlan!: StudentPlans | undefined;

  selectedSuggestedTimeSlotIndex!: number | undefined;
  classTypes = ClassTypes;
  instructors!: Array<IdNameModel>;
  suggestedTimeSlots!: SuggestedTimeSlot[] | undefined;
  selectedTimeSlot!: string | null;
  maxDate = new Date();
  transformedDate = this.datePipe.transform(this.maxDate, this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss);

  @Output() closeModal = new EventEmitter<void>();
  @Output() refreshScheduleData = new EventEmitter<void>();

  constructor(
    protected readonly schedulerService: SchedulerService,
    private readonly groupClassService: GroupClassesService,
    private readonly ensembleClassService: EnsembleClassesService,
    private readonly studentPlanService: StudentPlanService,
    private readonly summerCampScheduleService: SummerCampScheduleService,
    private readonly cdr: ChangeDetectorRef,
    private readonly datePipe: DatePipe,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initUpdateScheduleForm();
  }

  ngAfterViewInit(): void {
    this.getDetailsBasedOnClassType();
  }

  initUpdateScheduleForm(): void {
    this.updateScheduleForm = new FormGroup<UpdateScheduleFormGroup>(
      {
        classType: new FormControl(undefined, { nonNullable: true }),
        id: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
        isAllInstances: new FormControl(false, { nonNullable: true }),
        scheduleStartDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
        scheduleEndDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
        daysOfSchedule: new FormArray([] as FormControl<number>[]),
        scheduleStartTime: new FormControl('', {
          nonNullable: true,
          validators: [Validators.required, outOfRangeTimeValidator()]
        }),
        scheduleEndTime: new FormControl('', {
          nonNullable: true,
          validators: [Validators.required, outOfRangeTimeValidator()]
        }),
        isNotifyClients: new FormControl(false, { nonNullable: true }),
        notes: new FormControl('', { nonNullable: true }),
        isSpecialNeedsLesson: new FormControl(false, { nonNullable: true }),
        instructorId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] })
      },
      { validators: timeRangeValidator('scheduleStartTime', 'scheduleEndTime') } as AbstractControlOptions
    );
  }

  getDetailsBasedOnClassType(): void {
    if (this.selectedEvent?.classType === this.classTypes.GROUP_CLASS) {
      this.getGroupClassDetail(this.selectedEvent.groupClassScheduleId);
    } else {
      this.getScheduleDetails(this.selectedEvent);
    }
  }

  getScheduleDetails(scheduleInfo: ScheduleDetailsView): void {
    this.scheduleInfo = scheduleInfo;
    if (scheduleInfo?.classType === this.classTypes.SUMMER_CAMP) {
      this.setUpdateSummerCampFormData();
      this.getInstructors();
    } else if (scheduleInfo?.classType === this.classTypes.ENSEMBLE_CLASS) {
      this.setUpdateScheduleFormData();
      this.getSuggestedTime(false, scheduleInfo.classType);
    } else {
      this.setUpdateScheduleFormData();
      this.getStudentPlans(scheduleInfo?.studentDetails[0]?.studentId);
      this.getSuggestedTime(false, scheduleInfo.classType);
    }
  }

  getGroupClassDetail(id: number): void {
    this.showPageLoader = true;
    this.groupClassService
      .get<CBGetResponse<GroupClassView>>(`${API_URL.groupClassScheduleSummaries.getGroupClassScheduleSummaryForView}?id=${id}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<GroupClassView>) => {
          this.groupClassInfo = res.result;
          this.setUpdateGroupClassFormData();
          this.getSuggestedTime(false, this.classTypes.GROUP_CLASS);
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getStudentPlans(studentId: number): void {
    this.showPageLoader = true;
    this.studentPlanService
      .getList<CBResponse<StudentPlans>>(`${API_URL.studentPlans.getStudentPlans}?DependentInformationId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentPlans>) => {
          this.assignedPlan = res.result.items.find(item => item.studentplan.id === this.scheduleInfo?.planId);
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  findVisitsPerWeekInPlan(plan: StudentPlans | undefined): number {
    return plan?.planDetails[0].planDetail.visitsPerWeek!;
  }

  setUpdateScheduleFormData(): void {
    this.updateScheduleForm.patchValue({
      ...this.scheduleInfo,
      scheduleStartDate: this.datePipe.transform(this.scheduleInfo?.scheduleDate, this.constants.dateFormats.yyyy_MM_dd),
      scheduleEndDate: this.datePipe.transform(this.scheduleInfo?.scheduleDate, this.constants.dateFormats.yyyy_MM_dd),
      scheduleEndTime: this.scheduleInfo?.end,
      scheduleStartTime: this.scheduleInfo?.start
    });
    this.selectedTimeSlot = `${this.datePipe.transform(
      DateUtils.toLocal(this.scheduleInfo?.start),
      'shortTime'
    )} - ${this.datePipe.transform(DateUtils.toLocal(this.scheduleInfo?.end), 'shortTime')}`;
  }

  setUpdateGroupClassFormData(): void {
    const groupClassInfo = this.groupClassInfo?.groupClassScheduleSummary;
    this.updateScheduleForm.patchValue({ ...groupClassInfo, daysOfSchedule: [Number(groupClassInfo?.scheduleDays)] });
    this.selectedTimeSlot = `${this.datePipe.transform(
      DateUtils.toLocal(groupClassInfo?.scheduleStartTime),
      'shortTime'
    )} - ${this.datePipe.transform(
      DateUtils.toLocal(groupClassInfo?.scheduleEndTime),
      'shortTime'
    )}`;
  }

  getInstructors(): void {
    const control = this.getInstructorAvailability;
    if (
      control.locationId &&
      control.scheduleStartDate &&
      control.scheduleEndDate &&
      control.scheduleStartTime &&
      control.scheduleEndTime
    ) {
      this.summerCampScheduleService
        .add(this.getInstructorAvailability, API_URL.summerCampScheduleSummaries.getAvailableInstructors)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<IdNameModel[]>) => {
            this.instructors = response.result;
            this.cdr.detectChanges();
          }
        });
    }
  }

  setUpdateSummerCampFormData(): void {
    this.updateScheduleForm.patchValue({
      ...this.scheduleInfo,
      scheduleStartDate: this.datePipe.transform(this.scheduleInfo?.scheduleDate, this.constants.dateFormats.yyyy_MM_dd),
      scheduleEndDate: this.datePipe.transform(this.scheduleInfo?.scheduleDate, this.constants.dateFormats.yyyy_MM_dd),
      scheduleEndTime: this.datePipe.transform(DateUtils.toLocal(this.scheduleInfo?.end), 'shortTime') ?? '',
      scheduleStartTime: this.datePipe.transform(DateUtils.toLocal(this.scheduleInfo?.start), 'shortTime') ?? '',
      instructorId: this.selectedEvent?.instructorId
    });
    this.setTimeInit('scheduleStartTime');
    this.setTimeInit('scheduleEndTime');
  }

  setTimeInit(control: string): void {
    const startDate = this.datePipe.transform(
      this.updateScheduleForm.controls.scheduleStartDate.value,
      this.constants.dateFormats.yyyy_MM_dd
    );
    const timeValue = CommonUtils.combineDateAndTime(startDate ?? '', this.updateScheduleForm.get(control)?.value);

    this.updateScheduleForm?.get(control)?.setValue(timeValue);
  }

  resetInstructor(): void {
    if (this.selectedEvent && this.selectedEvent.classType === this.classTypes.SUMMER_CAMP) {
      this.selectedEvent.instructorName = undefined;
      this.updateScheduleForm.controls.instructorId?.reset();
      this.getInstructors();
    }
  }

  get getInstructorAvailability(): InstructorAvaibility {
    const duration = this.getTimeDiff(this.selectedEvent?.start!, this.selectedEvent?.end!);
    return {
      classType: this.selectedEvent?.classType,
      scheduleStartDate: DateUtils.getUtcRangeForLocalDate(this.updateScheduleForm.controls.scheduleStartDate.value ?? '').startUtc,
      scheduleEndDate:
        this.selectedEvent?.classType === this.classTypes.GROUP_CLASS && this.updateScheduleForm.getRawValue().isAllInstances
          ? DateUtils.getUtcRangeForLocalDate(this.updateScheduleForm.controls.scheduleEndDate.value ?? '').endUtc
          : DateUtils.getUtcRangeForLocalDate(this.updateScheduleForm.controls.scheduleStartDate.value ?? '').endUtc,
      daysOfSchedule: this.updateScheduleForm?.controls.daysOfSchedule.value,
      instructorIds: this.scheduleInfo?.assignedInstructors?.map(instructor => instructor.id),
      isAllInstances: this.updateScheduleForm?.controls.isAllInstances.value ?? false,
      instructorId: this.selectedEvent?.instructorId,
      planId: this.scheduleInfo?.planId ?? null,
      locationId: this.selectedEvent?.locationId,
      duration: this.groupClassInfo?.groupClassScheduleSummary.duration ?? duration,
      scheduleStartTime: DateUtils.toLocal(this.updateScheduleForm.controls.scheduleStartTime.value),
      scheduleEndTime: DateUtils.toLocal(this.updateScheduleForm.controls.scheduleEndTime.value),
      studentId: this.scheduleInfo?.studentDetails[0]?.studentId
    };
  }

  @Debounce(500)
  getSuggestedTime(clearScheduleDate: boolean, type: number): void {
    if (this.getInstructorAvailability.scheduleStartDate && this.getInstructorAvailability.scheduleEndDate) {
      if (type === this.classTypes.ENSEMBLE_CLASS) {
        this.ensembleClassService
          .add(this.getInstructorAvailability, API_URL.ensembleClassesScheduleSummaries.getInstructorAvaibilableTimeSlots)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (response: CBGetResponse<SuggestedTimeSlot[]>) => {
              this.suggestedTimeSlots = response.result.map(timeSlot => ({
                ...timeSlot,
                startTime: DateUtils.toLocal(timeSlot.startTime),
                endTime: DateUtils.toLocal(timeSlot.endTime)
              }));
              if (clearScheduleDate) {
                this.selectedTimeSlot = null;
                this.updateScheduleForm.controls.scheduleStartTime.reset();
              }
              this.cdr.detectChanges();
            }
          });
      } else {
        this.schedulerService
          .add(this.getInstructorAvailability, API_URL.scheduleLessonDetails.getInstructorAvaibility)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (response: CBGetResponse<SuggestedTimeSlot[]>) => {
              this.suggestedTimeSlots = response.result.map(timeSlot => ({
                ...timeSlot,
                startTime: DateUtils.toLocal(timeSlot.startTime),
                endTime: DateUtils.toLocal(timeSlot.endTime)
              }));
              if (clearScheduleDate) {
                this.selectedTimeSlot = null;
                this.updateScheduleForm.controls.scheduleStartTime.reset();
              }
              this.cdr.detectChanges();
            }
          });
      }
    }
  }

  setTimeOnDateChange(control: string): void {
    const startDate = this.datePipe.transform(
      this.updateScheduleForm.controls.scheduleStartDate.value,
      this.constants.dateFormats.yyyy_MM_dd
    );
    const formattedTime = this.datePipe.transform(this.updateScheduleForm.get(control)?.value, 'shortTime');
    const timeValue = CommonUtils.combineDateAndTime(startDate ?? '', formattedTime ?? '');

    this.updateScheduleForm?.get(control)?.setValue(timeValue);
  }

  clearScheduleEndDate(): void {
    if (this.updateScheduleForm.controls.isAllInstances.value && this.selectedEvent?.groupClassScheduleId) {
      this.updateScheduleForm.controls.scheduleEndDate.reset();
    }
    if (this.selectedEvent?.classType !== this.classTypes.SUMMER_CAMP) {
      this.getSuggestedTime(true, this.selectedEvent?.classType);
    } else {
      this.updateScheduleForm.controls.instructorId?.reset();
      this.setTimeOnDateChange('scheduleStartTime');
      this.setTimeOnDateChange('scheduleEndTime');
    }
  }

  setStartAndEndTime(selectedTimeSlot: SuggestedTimeSlot): void {
    this.selectedTimeSlot = `${selectedTimeSlot.startTime} - ${selectedTimeSlot.endTime}`;
    this.updateScheduleForm.patchValue({
      scheduleStartTime: selectedTimeSlot.startTime,
      scheduleEndTime: selectedTimeSlot.endTime
    });
  }

  onUpdateSchedule(): void {
    const scheduleValue = this.updateScheduleForm.getRawValue();
    if (this.updateScheduleForm.invalid) {
      this.updateScheduleForm.markAllAsTouched();
      return;
    }
    this.updateScheduleForm.markAsUntouched();
    this.showBtnLoader = true;
    this.schedulerService
      .update(
        {
          ...scheduleValue,
          id: this.selectedEvent?.id,
          classType: this.selectedEvent?.classType,
          scheduleStartDate: CommonUtils.combineDateAndTime(
            scheduleValue.scheduleStartDate ?? '',
            scheduleValue.scheduleStartTime
          ),
          scheduleEndDate:
            scheduleValue.isAllInstances ? CommonUtils.combineDateAndTime(
                scheduleValue.scheduleEndDate ?? '',
                scheduleValue.scheduleEndTime
              )
              : CommonUtils.combineDateAndTime(
                scheduleValue.scheduleStartDate ?? '',
                scheduleValue.scheduleEndTime
              ),
          scheduleStartTime: DateUtils.toUTC(scheduleValue.scheduleStartTime, 'yyyy-MM-DDTHH:mm:ss'),
          daysOfSchedule: scheduleValue.daysOfSchedule,
          scheduleEndTime: DateUtils.toUTC(scheduleValue.scheduleEndTime, 'yyyy-MM-DDTHH:mm:ss')
        },
        API_URL.crud.update
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.onCloseModal();
          this.refreshScheduleData.emit();
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstructorNames(array: AssignedInstructors[]) {
    return array
      .slice(1)
      .map(item => item.name)
      .join(', ');
  }

  setDaysOfWeek(dayValue: number): void {
    const daysOfSchedule = this.updateScheduleForm.get('daysOfSchedule') as FormArray;
    const index = daysOfSchedule.value.indexOf(dayValue);

    if (this.selectedEvent?.classType === this.classTypes.GROUP_CLASS) {
      this.setFormControlValue('daysOfSchedule', [Number(dayValue)]);
    } else if (this.selectedEvent?.classType === this.classTypes.ENSEMBLE_CLASS) {
      this.setFormControlValue('daysOfSchedule', [Number(dayValue)]);
    } else {
      if (index !== -1) {
        daysOfSchedule.removeAt(index);
      } else if (daysOfSchedule.length < this.findVisitsPerWeekInPlan(this.assignedPlan)) {
        daysOfSchedule.push(new FormControl(dayValue, { nonNullable: true }));
      } else {
        this.toasterService.error(this.constants.errorMessages.maxDaysSelected);
        return;
      }
    }

    this.getSuggestedTime(false, this.selectedEvent?.classType);
  }

  isDaySelected(dayValue: number): boolean | undefined {
    const daysOfSchedule = this.updateScheduleForm.get('daysOfSchedule')?.value;
    return daysOfSchedule?.includes(dayValue);
  }

  setFormControlValue(controlName: string, value: number | string | boolean | number[]): void {
    (this.updateScheduleForm.controls as any)[controlName].setValue(value);
  }

  onRecurringEditOptionChange(editSeries: boolean, type: number): void {
    this.setRequiredBasedOnCondition('daysOfSchedule', editSeries);
    this.updateScheduleForm.controls.daysOfSchedule.clear();

    if (editSeries) {
      if (this.scheduleInfo?.daysOfSchedule) {
        this.scheduleInfo.daysOfSchedule.forEach(day => {
          this.updateScheduleForm.controls.daysOfSchedule.push(new FormControl(Number(day), { nonNullable: true }));
        });
      }

      if (this.groupClassInfo?.groupClassScheduleSummary?.scheduleDays) {
        const scheduleDays = this.groupClassInfo.groupClassScheduleSummary.scheduleDays;
        this.updateScheduleForm.controls.daysOfSchedule.push(new FormControl(Number(scheduleDays), { nonNullable: true }));
      }
    }
    this.getSuggestedTime(false, type);
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean): void {
    const control = this.updateScheduleForm.get(controlName);
    if (required) {
      control?.setValidators([Validators.required]);
    } else {
      control?.clearValidators();
    }
    control?.updateValueAndValidity();
  }

  resetAddScheduleForm(): void {
    this.updateScheduleForm.reset();
    this.updateScheduleForm.controls.daysOfSchedule.clear();
  }

  getTimeDiff(start: string, end: string): number | null {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  onCloseModal(): void {
    this.resetAddScheduleForm();
    this.closeModal.emit();
  }
}
